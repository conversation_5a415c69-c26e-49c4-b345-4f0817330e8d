<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasPermissions;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasRoles, HasFactory, Notifiable, HasApiTokens, HasPermissions;


    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $table = 'user';
    public $timestamps = false;
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'profileDataForPhpMaker',
    ];



    public function parent()
    {
        if (DB::getDatabaseName() != env("DB_DATABASE")) {
            return $this->belongsTo(User::class, 'parentUser_id', 'id');
        }
    }

    // Relationship to get child users
    public function children()
    {
        if (DB::getDatabaseName() != env("DB_DATABASE")) {
            return $this->hasMany(User::class, 'parentUser_id', 'id');
        }
    }

    public function getAllChildUserIds($userId = null)
    {
        if (DB::getDatabaseName() != env("DB_DATABASE")) {
            $referenceUser = $userId ? self::find($userId) : $this;

            // Try to use CTE (Common Table Expression) for MySQL 8.0+
            try {
                $query = "
                    WITH RECURSIVE user_hierarchy AS (
                        SELECT id, parentUser_id, 1 as level
                        FROM user
                        WHERE id = ?

                        UNION ALL

                        SELECT u.id, u.parentUser_id, uh.level + 1
                        FROM user u
                        INNER JOIN user_hierarchy uh ON u.parentUser_id = uh.id
                        WHERE uh.level < 10  -- Prevent infinite recursion
                    )
                    SELECT id FROM user_hierarchy
                ";

                $results = DB::select($query, [$referenceUser->id]);
                return collect($results)->pluck('id');

            } catch (\Exception $e) {
                // Fallback to optimized iterative approach for older MySQL versions
                return $this->getAllChildUserIdsIterative($referenceUser->id);
            }
        }
        return collect([]);
    }

    /**
     * Optimized iterative approach for getting child user IDs (fallback for older MySQL)
     */
    private function getAllChildUserIdsIterative($userId)
    {
        $allIds = collect([$userId]);
        $currentLevelIds = collect([$userId]);
        $maxLevels = 10; // Prevent infinite loops
        $level = 0;

        while ($currentLevelIds->isNotEmpty() && $level < $maxLevels) {
            // Get all children of current level users in one query
            $nextLevelIds = self::whereIn('parentUser_id', $currentLevelIds)
                              ->pluck('id');

            if ($nextLevelIds->isEmpty()) {
                break;
            }

            // Add new IDs that we haven't seen before
            $newIds = $nextLevelIds->diff($allIds);
            $allIds = $allIds->merge($newIds);
            $currentLevelIds = $newIds;
            $level++;
        }

        return $allIds;
    }

    public function getAllChildUsers($userId = null)
    {
        if (DB::getDatabaseName() != env("DB_DATABASE")) {
            if (request()->user()->isAdmin() && $userId == null) {
                // Cache admin user list for 30 minutes and limit fields
                return Cache::remember('all_users_list', 1800, function() {
                    return User::select(['id', 'username'])
                              ->orderBy('username')
                              ->get()
                              ->toArray();
                });
            }

            $referenceUser = $userId ? self::find($userId) : $this;
            $userIds = $this->getAllChildUserIds($referenceUser->id);

            // Single query to get all users instead of recursive queries
            return User::whereIn('id', $userIds)
                      ->select('id', 'username')
                      ->orderBy('username')
                      ->get()
                      ->toArray();
        }
        return [];
    }


    /**
     * Check if the user is an administrator.
     *
     * @return bool
     */

    public function isAdmin()
    {
        if (DB::getDatabaseName() != env("DB_DATABASE")) {
            return $this->hasRole('Administrator') ? true : false;
        }
        return false;
    }
}
