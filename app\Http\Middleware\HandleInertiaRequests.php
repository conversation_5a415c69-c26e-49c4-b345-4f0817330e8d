<?php

namespace App\Http\Middleware;

use App\Helpers\Dashboard;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;
use Inertia\Middleware;

class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that is loaded on the first page visit.
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determine the current asset version.
     */
    public function version(Request $request): string|null
    {
        return parent::version($request);
    }

    /**
     * Define the props that are shared by default.
     *
     * @return array<string, mixed>
     */
    public function share(Request $request): array
    {
        $currentRoute = Route::currentRouteName();
        $user = $request->user();
        $isWorkspaceDB = DB::getDatabaseName() != env("DB_DATABASE");
        $cacheTime = 300; // 5 minutes

        // Cache menu counts for 5 minutes to avoid repeated database calls
        $currentPageData = [
            'menuCounts' => $user ? Cache::remember(
                "menu_counts_{$user->id}_{$currentRoute}",
                $cacheTime,
                fn() => menuCounts()
            ) : null
        ];

        // Add WhatsApp Business metadata only when needed
        if (str_starts_with($currentRoute, 'whatsappB.gateway')) {
            $currentPageData['metaAppId'] = env('WHATSAPP_BUSINESS_APP_ID');
            $currentPageData['metaAppVersion'] = env('WHATSAPP_BUSINESS_API_VERSION');
            $currentPageData['metaConfigId'] = env('WHATSAPP_BUSINESS_CONFIGURATION_ID');
        }

        return [
            ...parent::share($request),
            'auth' => [
                'user' => $user,
            ],
            'flash' => [
                'message' => fn() => $request->session()->get('message'),
                'type' => fn() => $request->session()->get('type')
            ],
            'currentPageData' => $currentPageData,
            'logo' => asset("assets/img/crmiconwhite.png"),
            'csrf_token' => csrf_token(),

            // Cache user permissions for 10 minutes - these rarely change
            'permissions' => $isWorkspaceDB && $user ? Cache::remember(
                "user_permissions_{$user->id}",
                $cacheTime,
                fn() => $user->getAllPermissions()->pluck('name')
            ) : null,

            // Cache side menu for 30 minutes - static menu structure
            'sideMenu' => $isWorkspaceDB && $user ? Cache::remember(
                "side_menu_{$user->id}",
                $cacheTime,
                fn() => Dashboard::sideMenu()->getData()
            ) : null,

            // Cache nav menu for 30 minutes - static menu structure
            'navMenu' => $isWorkspaceDB && $user ? Cache::remember(
                "nav_menu_{$user->id}",
                $cacheTime,
                fn() => Dashboard::navMenu()->getData()
            ) : null,

            // Cache child users for 15 minutes - user hierarchy changes infrequently
            'childUsers' => $isWorkspaceDB && $user ? Cache::remember(
                "child_users_{$user->id}",
                $cacheTime,
                fn() => $user->getAllChildUsers()
            ) : null,
        ];
    }
}
